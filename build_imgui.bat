@echo off
echo ========================================
echo Building KittyMemory ImGui IL2CPP Patcher
echo ========================================

echo.
echo Checking dependencies...

if not exist "../KittyMemoryEx" (
    echo ERROR: KittyMemoryEx directory not found!
    echo Please ensure KittyMemoryEx is in the parent directory.
    pause
    exit /b 1
)

if not exist "../ImGui" (
    echo ERROR: ImGui directory not found!
    echo Please ensure ImGui is in the parent directory.
    pause
    exit /b 1
)

echo Dependencies found.
echo.

echo Choose build method:
echo 1. NDK Build (recommended)
echo 2. CMake Build
echo.
set /p choice="Enter choice (1 or 2): "

if "%choice%"=="1" goto ndk_build
if "%choice%"=="2" goto cmake_build

echo Invalid choice. Using NDK Build by default.

:ndk_build
echo.
echo Building with NDK Build...
echo ========================================
call ndk_build.bat
goto end

:cmake_build
echo.
echo Building with CMake...
echo ========================================
call cmake_build.bat
goto end

:end
echo.
echo ========================================
echo Build completed!
echo.
echo To deploy to Android device:
echo 1. Connect your Android device with USB debugging enabled
echo 2. Run: adb devices (to verify connection)
echo 3. Run: push.bat (to deploy and run)
echo.
echo For custom process, edit push.bat and change:
echo   com.moonlight.monsterfusionwar
echo to your target process name.
echo ========================================
pause
