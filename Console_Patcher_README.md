# KittyMemory Console IL2CPP Patcher

Versi console-based dari KittyMemory IL2CPP Patcher yang dirancang khusus untuk executable Android yang dijalankan dari terminal.

## ✅ **Fitur Utama**

### 🎯 **Console Interface**
- Interface berbasis console yang mudah digunakan
- Tidak memerlukan OpenGL atau display
- Cocok untuk executable yang dijalankan via `adb shell`

### 🔧 **On/Off Functionality**
- Auto-enable semua patches secara default
- Manual control via command line arguments
- Restore memory otomatis saat exit

### ⚙️ **Custom Patch Support**
- Format: `il2cpp + base_offset + extra_offset`
- Contoh: `il2cpp + 0x1223443 + 0x10`
- Input via command line arguments

## 🚀 **Cara Penggunaan**

### **1. Basic Usage (Auto-enable semua patches)**
```bash
cd /data/local/tmp
./EnemySaga com.moonlight.monsterfusionwar
```

### **2. Manual Mode (tidak auto-enable)**
```bash
./EnemySaga com.moonlight.monsterfusionwar manual
```

### **3. Enable Patches Tertentu**
```bash
# Enable patch 1, 2, 3 (<PERSON>, ATK, DEF)
./EnemySaga com.moonlight.monsterfusionwar enable 123

# Enable patch 1 dan 5 (HP dan CRT)
./EnemySaga com.moonlight.monsterfusionwar enable 15
```

### **4. Custom Patch**
```bash
# Format: ./EnemySaga [process] custom [base_offset] [extra_offset] [hex_bytes]
./EnemySaga com.moonlight.monsterfusionwar custom 0x1223443 0x10 "E0 EF BF D2 C0 03 5F D6"

# Dengan hex bytes default
./EnemySaga com.moonlight.monsterfusionwar custom 0x1223443 0x10
```

## 📋 **Daftar Patches**

| ID | Nama | Offset | Hex Bytes |
|----|------|--------|-----------|
| 1  | HP | 0x2185F3C | E0 EF BF D2 C0 03 5F D6 |
| 2  | ATK | 0x2185FEC | E0 EF BF D2 C0 03 5F D6 |
| 3  | DEF | 0x2186098 | E0 EF BF D2 C0 03 5F D6 |
| 4  | SPEED | 0x2186144 | E0 EF BF D2 C0 03 5F D6 |
| 5  | CRT | 0x21861F0 | E0 EF BF D2 C0 03 5F D6 |
| 6  | CRTDMG | 0x218629C | E0 EF BF D2 C0 03 5F D6 |
| 7  | IMU | 0x2186348 | E0 EF BF D2 C0 03 5F D6 |
| 8  | REGEN | 0x21863F4 | E0 EF BF D2 C0 03 5F D6 |
| 9  | LIFESTEAL | 0x21864A0 | E0 EF BF D2 C0 03 5F D6 |
| 10 | STUNRATE | 0x218654C | E0 EF BF D2 C0 03 5F D6 |
| 11 | DODGE | 0x21865F8 | E0 EF BF D2 C0 03 5F D6 |
| 12 | COMBO_HIT | 0x21866A4 | E0 EF BF D2 C0 03 5F D6 |
| 13 | ACCURACY | 0x2186750 | E0 EF BF D2 C0 03 5F D6 |
| 14 | CurrentSpeed | 0x23A7C70 | E0 EF BF D2 C0 03 5F D6 |
| 15 | CurrentStrength | 0x23A1EDC | E0 EF BF D2 C0 03 5F D6 |
| 16 | MaxLevel | 0x214C01C | E0 7B 40 B2 C0 03 5F D6 |
| 17 | SkillLevel | 0x2217938 | E0 7B 40 B2 C0 03 5F D6 |

## 🔨 **Build & Deploy**

### **Build:**
```bash
# Clean build
ndk-build clean

# Build
ndk-build

# Atau gunakan script
./build_imgui.bat
```

### **Deploy:**
```bash
# Push ke device
adb push libs/arm64-v8a/EnemySaga /data/local/tmp

# Set permission
adb shell "su -c 'chmod +x /data/local/tmp/EnemySaga'"

# Run
adb shell "su -c '/data/local/tmp/EnemySaga com.moonlight.monsterfusionwar'"
```

## 📊 **Output Example**

```
I: Process Name: com.moonlight.monsterfusionwar
I: Process ID: 21495
I: ================ GET ELF BASE ===============
I: === KittyMemory IL2CPP Console Patcher ===
I: Waiting for IL2CPP base...
I: ✅ IL2CPP base found: 0x7B2C000000
I: ✅ Patches initialized and ready!

I: 📋 Available Patches:
I:   1. HP (0x2185F3C)
I:   2. ATK (0x2185FEC)
I:   3. DEF (0x2186098)
I:   ...

I: 🚀 Auto-enabling all patches...
I: HP has been enabled successfully
I: ATK has been enabled successfully
I: DEF has been enabled successfully
I: ...

I: 📊 Current Status:
I:   ✅ HP - ENABLED
I:   ✅ ATK - ENABLED
I:   ✅ DEF - ENABLED
I:   ...
I: 📈 Total: 17/17 patches enabled

I: 🔄 Keeping patches active...
I: ⏹️  Press Ctrl+C to exit and restore original memory
I: ⏰ Patches active for 1 minutes...
```

## 🛠️ **Troubleshooting**

### **Common Issues:**

1. **"Process not found"**
   - Pastikan target process sedang berjalan
   - Cek nama process dengan `ps | grep [nama]`

2. **"IL2CPP not found"**
   - Tunggu sampai app fully loaded
   - Pastikan app menggunakan IL2CPP (Unity games)

3. **"Permission denied"**
   - Pastikan device sudah root
   - Jalankan dengan `su -c`

4. **Patches tidak bekerja**
   - Cek offset apakah masih valid
   - Update offset sesuai versi app terbaru

### **Debug:**
```bash
# Monitor log output
adb logcat -s "KittyMemoryEx"

# Check process
adb shell "su -c 'ps | grep [process_name]'"

# Check memory maps
adb shell "su -c 'cat /proc/[pid]/maps | grep il2cpp'"
```

## 💡 **Tips**

- **Auto-enable mode** cocok untuk testing cepat
- **Manual mode** untuk kontrol lebih presisi  
- **Custom patch** untuk offset yang tidak ada di list
- Gunakan **Ctrl+C** untuk exit dengan aman (restore memory)
- Monitor logcat untuk debug info detail

## ⚠️ **Catatan Penting**

- **Root access required** untuk memory patching
- **Target process harus running** sebelum menjalankan patcher
- **Memory akan di-restore** otomatis saat exit
- **Backup original APK** sebelum testing
- **Gunakan dengan bijak** dan sesuai ToS game
