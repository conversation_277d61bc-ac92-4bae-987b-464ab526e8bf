LOCAL_PATH := $(call my-dir)

KITTYMEMORY_PATH = ../KittyMemoryEx
KITTYMEMORY_SRC = $(wildcard $(KITTYMEMORY_PATH)/*.cpp)

# IMGUI_PATH = ../ImGui
# IMGUI_SRC = $(wildcard $(IMGUI_PATH)/*.cpp) $(wildcard $(IMGUI_PATH)/backends/*.cpp)
# ImGui removed for console-only version

## Keystone static lib link
include $(CLEAR_VARS)
LOCAL_MODULE    := Keystone
LOCAL_SRC_FILES := $(KITTYMEMORY_PATH)/Deps/Keystone/libs-android/$(TARGET_ARCH_ABI)/libkeystone.a
include $(PREBUILT_STATIC_LIBRARY)

## Example exec
include $(CLEAR_VARS)

LOCAL_MODULE := EnemySaga

# add -DkITTYMEMORY_DEBUG for debug outputs
LOCAL_CPPFLAGS += -std=c++17 -<PERSON>IMGUI_IMPL_OPENGL_ES3 -fno-exceptions -fno-rtti

# Include paths for ImGui and KittyMemory
LOCAL_C_INCLUDES += $(IMGUI_PATH) $(IMGUI_PATH)/backends $(KITTYMEMORY_PATH)

LOCAL_SRC_FILES := Main.cpp $(KITTYMEMORY_SRC) $(IMGUI_SRC)

# Link libraries for OpenGL ES and EGL (needed for ImGui)
LOCAL_LDLIBS := -llog -lGLESv3 -lEGL -landroid

## add keystone
LOCAL_STATIC_LIBRARIES := Keystone

include $(BUILD_EXECUTABLE)