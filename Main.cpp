#include <thread>
#include <string>
#include <cstdint>
#include <vector>
#include <unordered_map>
#include <chrono>
#include <cstring>
#include <unistd.h>

// Note: ImGui includes removed for console-only version

// include KittyMemory
#include "../KittyMemoryEx/KittyMemoryMgr.hpp"

// Global variables
KittyMemoryMgr kittyMemMgr;
bool g_AppRunning = true;

// IL2CPP base address
uintptr_t il2cppBase = 0;
ElfScanner g_il2cppElf{};

// Patch structure
struct PatchInfo {
    std::string name;
    uintptr_t offset;
    std::string hexBytes;
    bool enabled;
    MemoryPatch patch;
    bool isCustom;

    PatchInfo(const std::string& n, uintptr_t off, const std::string& hex, bool custom = false)
        : name(n), offset(off), hexBytes(hex), enabled(false), isCustom(custom) {}
};

// Predefined patches
std::vector<PatchInfo> patches = {
    {"HP", 0x2185F3C, "E0 EF BF D2 C0 03 5F D6"},
    {"ATK", 0x2185FEC, "E0 EF BF D2 C0 03 5F D6"},
    {"DEF", 0x2186098, "E0 EF BF D2 C0 03 5F D6"},
    {"SPEED", 0x2186144, "E0 EF BF D2 C0 03 5F D6"},
    {"CRT", 0x21861F0, "E0 EF BF D2 C0 03 5F D6"},
    {"CRTDMG", 0x218629C, "E0 EF BF D2 C0 03 5F D6"},
    {"IMU", 0x2186348, "E0 EF BF D2 C0 03 5F D6"},
    {"REGEN", 0x21863F4, "E0 EF BF D2 C0 03 5F D6"},
    {"LIFESTEAL", 0x21864A0, "E0 EF BF D2 C0 03 5F D6"},
    {"STUNRATE", 0x218654C, "E0 EF BF D2 C0 03 5F D6"},
    {"DODGE", 0x21865F8, "E0 EF BF D2 C0 03 5F D6"},
    {"COMBO_HIT", 0x21866A4, "E0 EF BF D2 C0 03 5F D6"},
    {"ACCURACY", 0x2186750, "E0 EF BF D2 C0 03 5F D6"},
    {"CurrentSpeed", 0x23A7C70, "E0 EF BF D2 C0 03 5F D6"},
    {"CurrentStrength", 0x23A1EDC, "E0 EF BF D2 C0 03 5F D6"},
    {"MaxLevel", 0x214C01C, "E0 7B 40 B2 C0 03 5F D6"},
    {"SkillLevel", 0x2217938, "E0 7B 40 B2 C0 03 5F D6"}
};

// Custom patch variables removed - using command line arguments instead

// Helper functions
bool InitializeKittyMemory(const std::string& processName) {
    // get process ID
    pid_t processID = KittyMemoryEx::getProcessID(processName);
    if (!processID) {
        KITTY_LOGI("Couldn't find process id of %s.", processName.c_str());
        return false;
    }

    KITTY_LOGI("Process Name: %s", processName.c_str());
    KITTY_LOGI("Process ID: %d", processID);

    // initialize KittyMemoryMgr instance with process ID
    if (!kittyMemMgr.initialize(processID, EK_MEM_OP_SYSCALL, true)) {
        KITTY_LOGI("Error occurred )':");
        return false;
    }

    return true;
}

bool FindIL2CPPBase() {
    // loop until our target library is found
    do {
        std::this_thread::sleep_for(std::chrono::milliseconds(100));

        // get loaded elf
        g_il2cppElf = kittyMemMgr.getMemElf("libil2cpp.so");

        // in case lib is loaded from config apk
        for (auto& it : KittyMemoryEx::getAllMaps(kittyMemMgr.processID())) {
            if (KittyUtils::String::Contains(it.pathname, kittyMemMgr.processName()) &&
                KittyUtils::String::EndsWith(it.pathname, ".apk")) {
                g_il2cppElf = kittyMemMgr.getMemElfInZip(it.pathname, "libil2cpp.so");
                if (g_il2cppElf.isValid()) break;
            }
        }

    } while (!g_il2cppElf.isValid() && g_AppRunning);

    if (g_il2cppElf.isValid()) {
        il2cppBase = g_il2cppElf.base();
        KITTY_LOGI("libil2cpp.so base: %p", (void *)il2cppBase);
        return true;
    }
    return false;
}

void InitializePatches() {
    for (auto& patchInfo : patches) {
        patchInfo.patch = kittyMemMgr.memPatch.createWithHex(
            il2cppBase + patchInfo.offset,
            patchInfo.hexBytes
        );
    }
}

void TogglePatch(PatchInfo& patchInfo) {
    if (patchInfo.enabled) {
        if (patchInfo.patch.Modify()) {
            KITTY_LOGI("%s has been enabled successfully", patchInfo.name.c_str());
        }
    } else {
        if (patchInfo.patch.Restore()) {
            KITTY_LOGI("%s has been disabled successfully", patchInfo.name.c_str());
        }
    }
}

uintptr_t ParseHexString(const std::string& hexStr) {
    uintptr_t result = 0;
    std::string cleanHex = hexStr;

    // Remove 0x prefix if present
    if (cleanHex.length() >= 2 && (cleanHex.substr(0, 2) == "0x" || cleanHex.substr(0, 2) == "0X")) {
        cleanHex = cleanHex.substr(2);
    }

    // Manual hex parsing without exceptions
    for (char c : cleanHex) {
        result *= 16;
        if (c >= '0' && c <= '9') {
            result += c - '0';
        } else if (c >= 'A' && c <= 'F') {
            result += c - 'A' + 10;
        } else if (c >= 'a' && c <= 'f') {
            result += c - 'a' + 10;
        } else {
            // Invalid character, log error and return 0
            KITTY_LOGI("Error parsing hex string: invalid character '%c' in '%s'", c, hexStr.c_str());
            return 0;
        }
    }

    return result;
}

// RenderMainWindow function removed - using console interface instead

int main(int argc, char *args[])
{
    // ./exe [target process name]
    if (argc < 2) {
        KITTY_LOGE("Missing arg (process name).");
        return 1;
    }

    std::string processName = args[1];

    // Initialize KittyMemory
    if (!InitializeKittyMemory(processName)) {
        return 1;
    }

    KITTY_LOGI("================ GET ELF BASE ===============");

    // Find IL2CPP base in a separate thread
    std::thread il2cppThread([]() {
        if (FindIL2CPPBase()) {
            InitializePatches();
            KITTY_LOGI("Patches initialized successfully");
        }
    });

    // Console-based patcher for executable usage (no ImGui rendering)
    KITTY_LOGI("=== KittyMemory IL2CPP Console Patcher ===");
    KITTY_LOGI("Waiting for IL2CPP base...");

    // Wait for IL2CPP base to be found
    while (il2cppBase == 0 && g_AppRunning) {
        std::this_thread::sleep_for(std::chrono::milliseconds(500));
        KITTY_LOGI("Still searching for IL2CPP...");
    }

    if (il2cppBase != 0) {
        KITTY_LOGI("✅ IL2CPP base found: 0x%lX", il2cppBase);
        KITTY_LOGI("✅ Patches initialized and ready!");

        // Show available patches
        KITTY_LOGI("");
        KITTY_LOGI("📋 Available Patches:");
        for (size_t i = 0; i < patches.size(); i++) {
            KITTY_LOGI("  %zu. %s (0x%lX)",
                      i + 1,
                      patches[i].name.c_str(),
                      patches[i].offset);
        }

        // Check command line arguments for specific actions
        bool autoEnable = true;
        if (argc >= 3) {
            std::string action = args[2];
            if (action == "manual") {
                autoEnable = false;
                KITTY_LOGI("");
                KITTY_LOGI("🔧 Manual mode - patches not auto-enabled");
                KITTY_LOGI("💡 Usage examples:");
                KITTY_LOGI("   ./EnemySaga %s enable 1,2,3", processName.c_str());
                KITTY_LOGI("   ./EnemySaga %s custom 0x1223443 0x10", processName.c_str());
            } else if (action == "enable" && argc >= 4) {
                autoEnable = false;
                std::string patchIds = args[3];
                KITTY_LOGI("🔧 Enabling specific patches: %s", patchIds.c_str());

                // Parse comma-separated patch IDs (simple version)
                for (char c : patchIds) {
                    if (c >= '1' && c <= '9') {
                        int id = c - '0';
                        if (id >= 1 && id <= (int)patches.size()) {
                            auto& patch = patches[id - 1];
                            patch.enabled = true;
                            TogglePatch(patch);
                            KITTY_LOGI("  ✅ Enabled: %s", patch.name.c_str());
                        }
                    }
                }
            } else if (action == "custom" && argc >= 5) {
                autoEnable = false;
                uintptr_t baseOffset = ParseHexString(args[3]);
                uintptr_t extraOffset = ParseHexString(args[4]);
                std::string hexBytes = "E0 EF BF D2 C0 03 5F D6"; // Default
                if (argc >= 6) hexBytes = args[5];

                uintptr_t finalOffset = baseOffset + extraOffset;
                KITTY_LOGI("🔧 Adding custom patch: il2cpp + 0x%lX + 0x%lX = 0x%lX",
                          baseOffset, extraOffset, il2cppBase + finalOffset);

                PatchInfo customPatch("Custom", finalOffset, hexBytes, true);
                customPatch.patch = kittyMemMgr.memPatch.createWithHex(
                    il2cppBase + finalOffset,
                    hexBytes
                );
                customPatch.enabled = true;
                TogglePatch(customPatch);
                patches.push_back(customPatch);
                KITTY_LOGI("  ✅ Custom patch added and enabled!");
            }
        }

        if (autoEnable) {
            // Auto-enable all patches
            KITTY_LOGI("");
            KITTY_LOGI("🚀 Auto-enabling all patches...");
            for (auto& patch : patches) {
                if (!patch.enabled) {
                    patch.enabled = true;
                    TogglePatch(patch);
                }
            }
            KITTY_LOGI("✅ All patches are now ACTIVE!");
        }

        // Show current status
        KITTY_LOGI("");
        KITTY_LOGI("📊 Current Status:");
        int enabledCount = 0;
        for (const auto& patch : patches) {
            if (patch.enabled) {
                enabledCount++;
                KITTY_LOGI("  ✅ %s - ENABLED", patch.name.c_str());
            }
        }
        KITTY_LOGI("📈 Total: %d/%zu patches enabled", enabledCount, patches.size());

        // Keep running
        KITTY_LOGI("");
        KITTY_LOGI("🔄 Keeping patches active...");
        KITTY_LOGI("⏹️  Press Ctrl+C to exit and restore original memory");

        // Simple loop to keep patches active
        int counter = 0;
        while (g_AppRunning && counter < 3600) { // Run for 1 hour max
            std::this_thread::sleep_for(std::chrono::seconds(10));
            counter += 10;

            if (counter % 60 == 0) { // Every minute
                KITTY_LOGI("⏰ Patches active for %d minutes...", counter / 60);
            }
        }

        // Disable all patches before exit
        KITTY_LOGI("");
        KITTY_LOGI("🔄 Restoring original memory...");
        for (auto& patch : patches) {
            if (patch.enabled) {
                patch.enabled = false;
                TogglePatch(patch);
            }
        }
        KITTY_LOGI("✅ Memory restored to original state");
    } else {
        KITTY_LOGI("❌ Failed to find IL2CPP base address");
        KITTY_LOGI("💡 Make sure the target process is running and IL2CPP is loaded");
    }

    // Wait for IL2CPP thread to finish
    if (il2cppThread.joinable()) {
        il2cppThread.join();
    }

    KITTY_LOGI("Application terminated");
    return 0;
}