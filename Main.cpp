#include <thread>

#include <string>
#include <cstdint>
#include <vector>
#include "../ImGui/imgui.h"
#include "../ImGui/backends/imgui_impl_opengl3.h"

// include KittyMemory
#include "../KittyMemoryEx/KittyMemoryMgr.hpp"
KittyMemoryMgr kittyMemMgr;

bool g_Initialize_ImGui = false;

int main(int argc, char *args[])
{
    // ./exe [target process name]
    if (argc < 2)
    {
        KITTY_LOGE("Missing arg (process name).");
        return 1;
    }

    std::string processName = args[1];
    // get process ID
    pid_t processID = KittyMemoryEx::getProcessID(processName);
    if (!processID)
    {
        KITTY_LOGI("Couldn't find process id of %s.", processName.c_str());
        return 1;
    }

    KITTY_LOGI("Process Name: %s", processName.c_str());
    KITTY_LOGI("Process ID: %d", processID);

    // initialize KittyMemoryMgr instance with process ID
    if (!kittyMemMgr.initialize(processID, EK_MEM_OP_SYSCALL, true))
    {
        KITTY_LOGI("Error occurred )':");
        return 1;
    }

    KITTY_LOGI("================ GET ELF BASE ===============");
    
    ElfScanner g_il2cppElf{};
    // loop until our target library is found
    do
    {
        sleep(1);
        
        // get loaded elf
        g_il2cppElf = kittyMemMgr.getMemElf("libil2cpp.so");

        // in case lib is loaded from config apk
        for (auto& it : KittyMemoryEx::getAllMaps(kittyMemMgr.processID()))
        {
            if (KittyUtils::String::Contains(it.pathname, kittyMemMgr.processName()) && KittyUtils::String::EndsWith(it.pathname, ".apk"))
            {
                g_il2cppElf = kittyMemMgr.getMemElfInZip(it.pathname, "libil2cpp.so");
                if (g_il2cppElf.isValid()) break;
            }
        }

    } while (!g_il2cppElf.isValid());
    
    uintptr_t il2cppBase = g_il2cppElf.base();
    KITTY_LOGI("libil2cpp.so base: %p", (void *)il2cppBase);

    
    KITTY_LOGI("================ MEMORY READ & WRITE ===============");

    // read & write memory (address, buffer, buffer_size)
    char magic[16] = {0};
    size_t bytesRead = kittyMemMgr.readMem(il2cppBase, magic, sizeof(magic));
    KITTY_LOGI("bytesRead: 0x%zx", bytesRead);
	
    KITTY_LOGI("==================== SYMBOL LOOKUP ===================");

    KITTY_LOGI("il2cpp elf valid = %d", g_il2cppElf.isValid() ? 1 : 0);
    KITTY_LOGI("il2cpp_string_new = %p", (void *)g_il2cppElf.findSymbol("il2cpp_string_new"));


    KITTY_LOGI("==================== MEMORY PATCH ===================");

    // int function
    MemoryPatch get_HP;
    MemoryPatch get_ATK;
    MemoryPatch get_DEF;
    MemoryPatch get_SPEED;
    MemoryPatch get_CRT;
    MemoryPatch get_CRTDMG;
    MemoryPatch get_IMU;
    MemoryPatch get_REGEN;
    MemoryPatch get_LIFESTEAL;
    MemoryPatch get_STUNRATE;
    MemoryPatch get_DODGE;
    MemoryPatch get_COMBO_HIT;
    MemoryPatch get_ACCURACY;
    MemoryPatch get_CurrentSpeed;
    MemoryPatch get_CurrentStrength;
    MemoryPatch get_MaxLevel;
    MemoryPatch get_SkillLevel;

    get_HP = kittyMemMgr.memPatch.createWithHex(il2cppBase + 0x2185F3C, "E0 EF BF D2 C0 03 5F D6");
    get_ATK = kittyMemMgr.memPatch.createWithHex(il2cppBase + 0x2185FEC, "E0 EF BF D2 C0 03 5F D6");
    get_DEF = kittyMemMgr.memPatch.createWithHex(il2cppBase + 0x2186098, "E0 EF BF D2 C0 03 5F D6");
    get_SPEED = kittyMemMgr.memPatch.createWithHex(il2cppBase + 0x2186144, "E0 EF BF D2 C0 03 5F D6");
    get_CRT = kittyMemMgr.memPatch.createWithHex(il2cppBase + 0x21861F0, "E0 EF BF D2 C0 03 5F D6");
    get_CRTDMG = kittyMemMgr.memPatch.createWithHex(il2cppBase + 0x218629C, "E0 EF BF D2 C0 03 5F D6");
    get_IMU = kittyMemMgr.memPatch.createWithHex(il2cppBase + 0x2186348, "E0 EF BF D2 C0 03 5F D6");
    get_REGEN = kittyMemMgr.memPatch.createWithHex(il2cppBase + 0x21863F4, "E0 EF BF D2 C0 03 5F D6");
    get_LIFESTEAL = kittyMemMgr.memPatch.createWithHex(il2cppBase + 0x21864A0, "E0 EF BF D2 C0 03 5F D6");
    get_STUNRATE = kittyMemMgr.memPatch.createWithHex(il2cppBase + 0x218654C, "E0 EF BF D2 C0 03 5F D6");
    get_DODGE = kittyMemMgr.memPatch.createWithHex(il2cppBase + 0x21865F8, "E0 EF BF D2 C0 03 5F D6");
    get_COMBO_HIT = kittyMemMgr.memPatch.createWithHex(il2cppBase + 0x21866A4, "E0 EF BF D2 C0 03 5F D6");
    get_ACCURACY = kittyMemMgr.memPatch.createWithHex(il2cppBase + 0x2186750, "E0 EF BF D2 C0 03 5F D6");
    get_CurrentSpeed = kittyMemMgr.memPatch.createWithHex(il2cppBase + 0x23A7C70, "E0 EF BF D2 C0 03 5F D6");
    get_CurrentStrength = kittyMemMgr.memPatch.createWithHex(il2cppBase + 0x23A1EDC, "E0 EF BF D2 C0 03 5F D6");
    get_MaxLevel = kittyMemMgr.memPatch.createWithHex(il2cppBase + 0x214C01C, "E0 7B 40 B2 C0 03 5F D6");
    get_SkillLevel = kittyMemMgr.memPatch.createWithHex(il2cppBase + 0x2217938, "E0 7B 40 B2 C0 03 5F D6");

    if (get_HP.Modify())
    {
        KITTY_LOGI("get_HP has been modified successfully");
        KITTY_LOGI("Current Bytes: %s", get_HP.get_CurrBytes().c_str());
    }

    if (get_ATK.Modify())
    {
        KITTY_LOGI("get_ATK has been modified successfully");
        KITTY_LOGI("Current Bytes: %s", get_ATK.get_CurrBytes().c_str());
    }

    if (get_DEF.Modify())
    {
        KITTY_LOGI("get_DEF has been modified successfully");
        KITTY_LOGI("Current Bytes: %s", get_DEF.get_CurrBytes().c_str());
    }

    if (get_SPEED.Modify())
    {
        KITTY_LOGI("get_SPEED has been modified successfully");
        KITTY_LOGI("Current Bytes: %s", get_SPEED.get_CurrBytes().c_str());
    }

    if (get_CRT.Modify())
    {
        KITTY_LOGI("get_CRT has been modified successfully");
        KITTY_LOGI("Current Bytes: %s", get_CRT.get_CurrBytes().c_str());
    }

    if (get_CRTDMG.Modify())
    {
        KITTY_LOGI("get_CRTDMG has been modified successfully");
        KITTY_LOGI("Current Bytes: %s", get_CRTDMG.get_CurrBytes().c_str());
    }

    if (get_IMU.Modify())
    {
        KITTY_LOGI("get_IMU has been modified successfully");
        KITTY_LOGI("Current Bytes: %s", get_IMU.get_CurrBytes().c_str());
    }

    if (get_REGEN.Modify())
    {
        KITTY_LOGI("get_REGEN has been modified successfully");
        KITTY_LOGI("Current Bytes: %s", get_REGEN.get_CurrBytes().c_str());
    }

    if (get_LIFESTEAL.Modify())
    {
        KITTY_LOGI("get_LIFESTEAL has been modified successfully");
        KITTY_LOGI("Current Bytes: %s", get_LIFESTEAL.get_CurrBytes().c_str());
    }

    if (get_STUNRATE.Modify())
    {
        KITTY_LOGI("get_STUNRATE has been modified successfully");
        KITTY_LOGI("Current Bytes: %s", get_STUNRATE.get_CurrBytes().c_str());
    }

    if (get_DODGE.Modify())
    {
        KITTY_LOGI("get_DODGE has been modified successfully");
        KITTY_LOGI("Current Bytes: %s", get_DODGE.get_CurrBytes().c_str());
    }

    if (get_COMBO_HIT.Modify())
    {
        KITTY_LOGI("get_COMBO_HIT has been modified successfully");
        KITTY_LOGI("Current Bytes: %s", get_COMBO_HIT.get_CurrBytes().c_str());
    }

    if (get_ACCURACY.Modify())
    {
        KITTY_LOGI("get_ACCURACY has been modified successfully");
        KITTY_LOGI("Current Bytes: %s", get_ACCURACY.get_CurrBytes().c_str());
    }
	
    if (get_CurrentSpeed.Modify())
    {
        KITTY_LOGI("get_CurrentSpeed has been modified successfully");
        KITTY_LOGI("Current Bytes: %s", get_CurrentSpeed.get_CurrBytes().c_str());
    }
	
    if (get_CurrentStrength.Modify())
    {
        KITTY_LOGI("get_CurrentStrength has been modified successfully");
        KITTY_LOGI("Current Bytes: %s", get_CurrentStrength.get_CurrBytes().c_str());
    }
	
    if (get_MaxLevel.Modify())
    {
        KITTY_LOGI("get_MaxLevel has been modified successfully");
        KITTY_LOGI("Current Bytes: %s", get_MaxLevel.get_CurrBytes().c_str());
    }
	
    if (get_SkillLevel.Modify())
    {
        KITTY_LOGI("get_SkillLevel has been modified successfully");
        KITTY_LOGI("Current Bytes: %s", get_SkillLevel.get_CurrBytes().c_str());
    }

    // modify & print bytes
    if (get_HP.Restore())
    {
        KITTY_LOGI("get_HP has been restored successfully");
        KITTY_LOGI("Current Bytes: %s", get_HP.get_CurrBytes().c_str());
    }

    if (get_ATK.Restore())
    {
        KITTY_LOGI("get_ATK has been modified successfully");
        KITTY_LOGI("Current Bytes: %s", get_ATK.get_CurrBytes().c_str());
    }

    if (get_DEF.Restore())
    {
        KITTY_LOGI("get_DEF has been modified successfully");
        KITTY_LOGI("Current Bytes: %s", get_DEF.get_CurrBytes().c_str());
    }

    if (get_SPEED.Restore())
    {
        KITTY_LOGI("get_SPEED has been modified successfully");
        KITTY_LOGI("Current Bytes: %s", get_SPEED.get_CurrBytes().c_str());
    }

    if (get_CRT.Restore())
    {
        KITTY_LOGI("get_CRT has been modified successfully");
        KITTY_LOGI("Current Bytes: %s", get_CRT.get_CurrBytes().c_str());
    }

    if (get_CRTDMG.Restore())
    {
        KITTY_LOGI("get_CRTDMG has been modified successfully");
        KITTY_LOGI("Current Bytes: %s", get_CRTDMG.get_CurrBytes().c_str());
    }

    if (get_IMU.Restore())
    {
        KITTY_LOGI("get_IMU has been modified successfully");
        KITTY_LOGI("Current Bytes: %s", get_IMU.get_CurrBytes().c_str());
    }

    if (get_REGEN.Restore())
    {
        KITTY_LOGI("get_REGEN has been modified successfully");
        KITTY_LOGI("Current Bytes: %s", get_REGEN.get_CurrBytes().c_str());
    }

    if (get_LIFESTEAL.Restore())
    {
        KITTY_LOGI("get_LIFESTEAL has been modified successfully");
        KITTY_LOGI("Current Bytes: %s", get_LIFESTEAL.get_CurrBytes().c_str());
    }

    if (get_STUNRATE.Restore())
    {
        KITTY_LOGI("get_STUNRATE has been modified successfully");
        KITTY_LOGI("Current Bytes: %s", get_STUNRATE.get_CurrBytes().c_str());
    }

    if (get_DODGE.Restore())
    {
        KITTY_LOGI("get_DODGE has been modified successfully");
        KITTY_LOGI("Current Bytes: %s", get_DODGE.get_CurrBytes().c_str());
    }

    if (get_COMBO_HIT.Restore())
    {
        KITTY_LOGI("get_COMBO_HIT has been modified successfully");
        KITTY_LOGI("Current Bytes: %s", get_COMBO_HIT.get_CurrBytes().c_str());
    }

    if (get_ACCURACY.Restore())
    {
        KITTY_LOGI("get_ACCURACY has been modified successfully");
        KITTY_LOGI("Current Bytes: %s", get_ACCURACY.get_CurrBytes().c_str());
    }
	
    if (get_CurrentSpeed.Restore())
    {
        KITTY_LOGI("get_CurrentSpeed has been modified successfully");
        KITTY_LOGI("Current Bytes: %s", get_CurrentSpeed.get_CurrBytes().c_str());
    }
	
    if (get_CurrentStrength.Restore())
    {
        KITTY_LOGI("get_CurrentStrength has been modified successfully");
        KITTY_LOGI("Current Bytes: %s", get_CurrentStrength.get_CurrBytes().c_str());
    }
	
    if (get_MaxLevel.Restore())
    {
        KITTY_LOGI("get_MaxLevel has been modified successfully");
        KITTY_LOGI("Current Bytes: %s", get_MaxLevel.get_CurrBytes().c_str());
    }
	
    if (get_SkillLevel.Restore())
    {
        KITTY_LOGI("get_SkillLevel has been modified successfully");
        KITTY_LOGI("Current Bytes: %s", get_SkillLevel.get_CurrBytes().c_str());
    }

    KITTY_LOGI("==================== MEMORY DUMP ====================");

    std::string dumpFolder = KittyUtils::getExternalStorage();
    bool isDumped = false;

    // dump memory elf
    std::string sodumpPath = dumpFolder + "/il2cpp_dump.so";
    isDumped = kittyMemMgr.dumpMemELF(il2cppBase, sodumpPath);
    KITTY_LOGI("il2cpp so dump = %d", isDumped ? 1 : 0);

    // dump memory file
    std::string datdumpPath = dumpFolder + "/global-metadata.dat";
    isDumped = kittyMemMgr.dumpMemFile("global-metadata.dat", datdumpPath);
    KITTY_LOGI("metadata dump = %d", isDumped ? 1 : 0);
    return 0;
}