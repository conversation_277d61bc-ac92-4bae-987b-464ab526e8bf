#include <thread>
#include <string>
#include <cstdint>
#include <vector>
#include <unordered_map>
#include <chrono>
#include <cstring>
#include <unistd.h>

// ImGui includes
#include "../ImGui/imgui.h"
#include "../ImGui/backends/imgui_impl_opengl3.h"
#include "../ImGui/backends/imgui_impl_android.h"

// include KittyMemory
#include "../KittyMemoryEx/KittyMemoryMgr.hpp"

// Global variables
KittyMemoryMgr kittyMemMgr;
bool g_Initialize_ImGui = false;
bool g_ShowMainWindow = true;
bool g_AppRunning = true;

// IL2CPP base address
uintptr_t il2cppBase = 0;
ElfScanner g_il2cppElf{};

// Patch structure
struct PatchInfo {
    std::string name;
    uintptr_t offset;
    std::string hexBytes;
    bool enabled;
    MemoryPatch patch;
    bool isCustom;

    PatchInfo(const std::string& n, uintptr_t off, const std::string& hex, bool custom = false)
        : name(n), offset(off), hexBytes(hex), enabled(false), isCustom(custom) {}
};

// Predefined patches
std::vector<PatchInfo> patches = {
    {"HP", 0x2185F3C, "E0 EF BF D2 C0 03 5F D6"},
    {"ATK", 0x2185FEC, "E0 EF BF D2 C0 03 5F D6"},
    {"DEF", 0x2186098, "E0 EF BF D2 C0 03 5F D6"},
    {"SPEED", 0x2186144, "E0 EF BF D2 C0 03 5F D6"},
    {"CRT", 0x21861F0, "E0 EF BF D2 C0 03 5F D6"},
    {"CRTDMG", 0x218629C, "E0 EF BF D2 C0 03 5F D6"},
    {"IMU", 0x2186348, "E0 EF BF D2 C0 03 5F D6"},
    {"REGEN", 0x21863F4, "E0 EF BF D2 C0 03 5F D6"},
    {"LIFESTEAL", 0x21864A0, "E0 EF BF D2 C0 03 5F D6"},
    {"STUNRATE", 0x218654C, "E0 EF BF D2 C0 03 5F D6"},
    {"DODGE", 0x21865F8, "E0 EF BF D2 C0 03 5F D6"},
    {"COMBO_HIT", 0x21866A4, "E0 EF BF D2 C0 03 5F D6"},
    {"ACCURACY", 0x2186750, "E0 EF BF D2 C0 03 5F D6"},
    {"CurrentSpeed", 0x23A7C70, "E0 EF BF D2 C0 03 5F D6"},
    {"CurrentStrength", 0x23A1EDC, "E0 EF BF D2 C0 03 5F D6"},
    {"MaxLevel", 0x214C01C, "E0 7B 40 B2 C0 03 5F D6"},
    {"SkillLevel", 0x2217938, "E0 7B 40 B2 C0 03 5F D6"}
};

// Custom patch variables
static char customPatchName[128] = "";
static char customOffset[32] = "0x1223443";
static char customExtraOffset[32] = "0x10";
static char customHexBytes[256] = "E0 EF BF D2 C0 03 5F D6";

// Helper functions
bool InitializeKittyMemory(const std::string& processName) {
    // get process ID
    pid_t processID = KittyMemoryEx::getProcessID(processName);
    if (!processID) {
        KITTY_LOGI("Couldn't find process id of %s.", processName.c_str());
        return false;
    }

    KITTY_LOGI("Process Name: %s", processName.c_str());
    KITTY_LOGI("Process ID: %d", processID);

    // initialize KittyMemoryMgr instance with process ID
    if (!kittyMemMgr.initialize(processID, EK_MEM_OP_SYSCALL, true)) {
        KITTY_LOGI("Error occurred )':");
        return false;
    }

    return true;
}

bool FindIL2CPPBase() {
    // loop until our target library is found
    do {
        std::this_thread::sleep_for(std::chrono::milliseconds(100));

        // get loaded elf
        g_il2cppElf = kittyMemMgr.getMemElf("libil2cpp.so");

        // in case lib is loaded from config apk
        for (auto& it : KittyMemoryEx::getAllMaps(kittyMemMgr.processID())) {
            if (KittyUtils::String::Contains(it.pathname, kittyMemMgr.processName()) &&
                KittyUtils::String::EndsWith(it.pathname, ".apk")) {
                g_il2cppElf = kittyMemMgr.getMemElfInZip(it.pathname, "libil2cpp.so");
                if (g_il2cppElf.isValid()) break;
            }
        }

    } while (!g_il2cppElf.isValid() && g_AppRunning);

    if (g_il2cppElf.isValid()) {
        il2cppBase = g_il2cppElf.base();
        KITTY_LOGI("libil2cpp.so base: %p", (void *)il2cppBase);
        return true;
    }
    return false;
}

void InitializePatches() {
    for (auto& patchInfo : patches) {
        patchInfo.patch = kittyMemMgr.memPatch.createWithHex(
            il2cppBase + patchInfo.offset,
            patchInfo.hexBytes
        );
    }
}

void TogglePatch(PatchInfo& patchInfo) {
    if (patchInfo.enabled) {
        if (patchInfo.patch.Modify()) {
            KITTY_LOGI("%s has been enabled successfully", patchInfo.name.c_str());
        }
    } else {
        if (patchInfo.patch.Restore()) {
            KITTY_LOGI("%s has been disabled successfully", patchInfo.name.c_str());
        }
    }
}

uintptr_t ParseHexString(const std::string& hexStr) {
    uintptr_t result = 0;
    std::string cleanHex = hexStr;

    // Remove 0x prefix if present
    if (cleanHex.length() >= 2 && (cleanHex.substr(0, 2) == "0x" || cleanHex.substr(0, 2) == "0X")) {
        cleanHex = cleanHex.substr(2);
    }

    // Manual hex parsing without exceptions
    for (char c : cleanHex) {
        result *= 16;
        if (c >= '0' && c <= '9') {
            result += c - '0';
        } else if (c >= 'A' && c <= 'F') {
            result += c - 'A' + 10;
        } else if (c >= 'a' && c <= 'f') {
            result += c - 'a' + 10;
        } else {
            // Invalid character, log error and return 0
            KITTY_LOGI("Error parsing hex string: invalid character '%c' in '%s'", c, hexStr.c_str());
            return 0;
        }
    }

    return result;
}

void RenderMainWindow() {
    if (!g_ShowMainWindow) return;

    ImGui::SetNextWindowSize(ImVec2(800, 600), ImGuiCond_FirstUseEver);
    if (ImGui::Begin("KittyMemory IL2CPP Patcher", &g_ShowMainWindow)) {

        // Status section
        ImGui::Text("IL2CPP Base: 0x%lX", il2cppBase);
        ImGui::Text("Process: %s (PID: %d)",
                   kittyMemMgr.processName().c_str(),
                   kittyMemMgr.processID());

        ImGui::Separator();

        // Predefined patches section
        if (ImGui::CollapsingHeader("Predefined Patches", ImGuiTreeNodeFlags_DefaultOpen)) {
            ImGui::Columns(3, "PatchColumns");
            ImGui::Text("Patch Name");
            ImGui::NextColumn();
            ImGui::Text("Offset");
            ImGui::NextColumn();
            ImGui::Text("Enable/Disable");
            ImGui::NextColumn();
            ImGui::Separator();

            for (auto& patchInfo : patches) {
                ImGui::Text("%s", patchInfo.name.c_str());
                ImGui::NextColumn();
                ImGui::Text("0x%lX", patchInfo.offset);
                ImGui::NextColumn();

                bool previousState = patchInfo.enabled;
                if (ImGui::Checkbox(("##" + patchInfo.name).c_str(), &patchInfo.enabled)) {
                    if (patchInfo.enabled != previousState) {
                        TogglePatch(patchInfo);
                    }
                }
                ImGui::NextColumn();
            }
            ImGui::Columns(1);
        }

        ImGui::Separator();

        // Custom patch section
        if (ImGui::CollapsingHeader("Custom Patch", ImGuiTreeNodeFlags_DefaultOpen)) {
            ImGui::Text("Create custom patch with format: il2cpp + offset + extra_offset");

            ImGui::InputText("Patch Name", customPatchName, sizeof(customPatchName));
            ImGui::InputText("Base Offset", customOffset, sizeof(customOffset));
            ImGui::InputText("Extra Offset", customExtraOffset, sizeof(customExtraOffset));
            ImGui::InputText("Hex Bytes", customHexBytes, sizeof(customHexBytes));

            if (ImGui::Button("Add Custom Patch")) {
                if (strlen(customPatchName) > 0 && strlen(customOffset) > 0) {
                    uintptr_t baseOffset = ParseHexString(customOffset);
                    uintptr_t extraOffset = ParseHexString(customExtraOffset);
                    uintptr_t finalOffset = baseOffset + extraOffset;

                    PatchInfo customPatch(customPatchName, finalOffset, customHexBytes, true);
                    customPatch.patch = kittyMemMgr.memPatch.createWithHex(
                        il2cppBase + finalOffset,
                        customHexBytes
                    );

                    patches.push_back(customPatch);

                    KITTY_LOGI("Added custom patch: %s at offset 0x%lX",
                              customPatchName, finalOffset);

                    // Clear input fields
                    memset(customPatchName, 0, sizeof(customPatchName));
                    strcpy(customOffset, "0x1223443");
                    strcpy(customExtraOffset, "0x10");
                }
            }
        }

        ImGui::Separator();

        // Control buttons
        if (ImGui::Button("Enable All Patches")) {
            for (auto& patchInfo : patches) {
                if (!patchInfo.enabled) {
                    patchInfo.enabled = true;
                    TogglePatch(patchInfo);
                }
            }
        }

        ImGui::SameLine();
        if (ImGui::Button("Disable All Patches")) {
            for (auto& patchInfo : patches) {
                if (patchInfo.enabled) {
                    patchInfo.enabled = false;
                    TogglePatch(patchInfo);
                }
            }
        }

        ImGui::SameLine();
        if (ImGui::Button("Exit")) {
            g_AppRunning = false;
        }
    }
    ImGui::End();
}

int main(int argc, char *args[])
{
    // ./exe [target process name]
    if (argc < 2) {
        KITTY_LOGE("Missing arg (process name).");
        return 1;
    }

    std::string processName = args[1];

    // Initialize KittyMemory
    if (!InitializeKittyMemory(processName)) {
        return 1;
    }

    KITTY_LOGI("================ GET ELF BASE ===============");

    // Find IL2CPP base in a separate thread
    std::thread il2cppThread([]() {
        if (FindIL2CPPBase()) {
            InitializePatches();
            KITTY_LOGI("Patches initialized successfully");
        }
    });

    // Initialize ImGui (Note: This is a simplified version for console app)
    // In a real Android app, you would need proper OpenGL context
    IMGUI_CHECKVERSION();
    ImGui::CreateContext();
    ImGuiIO& io = ImGui::GetIO(); (void)io;
    io.ConfigFlags |= ImGuiConfigFlags_NavEnableKeyboard;

    // Setup style
    ImGui::StyleColorsDark();

    // Main loop (simplified for console application)
    KITTY_LOGI("Starting ImGui interface...");

    while (g_AppRunning) {
        // In a real application, you would handle events and rendering here
        // For this console example, we'll just sleep and check for IL2CPP
        std::this_thread::sleep_for(std::chrono::milliseconds(100));

        if (il2cppBase != 0 && !g_Initialize_ImGui) {
            g_Initialize_ImGui = true;
            KITTY_LOGI("IL2CPP base found, ImGui interface ready");
            KITTY_LOGI("Use external ImGui renderer to connect to this process");
        }

        // Simulate ImGui frame (in real app this would be in render loop)
        if (g_Initialize_ImGui) {
            ImGui::NewFrame();
            RenderMainWindow();
            ImGui::Render();
        }
    }

    // Wait for IL2CPP thread to finish
    if (il2cppThread.joinable()) {
        il2cppThread.join();
    }

    // Cleanup
    ImGui::DestroyContext();

    KITTY_LOGI("Application terminated");
    return 0;
}