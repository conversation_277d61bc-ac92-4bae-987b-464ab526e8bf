#include <thread>
#include <string>
#include <cstdint>
#include <vector>
#include <iostream>
#include <sstream>
#include <cstring>
#include <unistd.h>

// include KittyMemory
#include "../KittyMemoryEx/KittyMemoryMgr.hpp"

// Global variables
KittyMemoryMgr kittyMemMgr;
bool g_AppRunning = true;

// IL2CPP base address
uintptr_t il2cppBase = 0;
ElfScanner g_il2cppElf{};

// Patch structure
struct PatchInfo {
    std::string name;
    uintptr_t offset;
    std::string hexBytes;
    bool enabled;
    MemoryPatch patch;
    bool isCustom;
    
    PatchInfo(const std::string& n, uintptr_t off, const std::string& hex, bool custom = false) 
        : name(n), offset(off), hexBytes(hex), enabled(false), isCustom(custom) {}
};

// Predefined patches
std::vector<PatchInfo> patches = {
    {"HP", 0x2185F3C, "E0 EF BF D2 C0 03 5F D6"},
    {"ATK", 0x2185FEC, "E0 EF BF D2 C0 03 5F D6"},
    {"DEF", 0x2186098, "E0 EF BF D2 C0 03 5F D6"},
    {"SPEED", 0x2186144, "E0 EF BF D2 C0 03 5F D6"},
    {"CRT", 0x21861F0, "E0 EF BF D2 C0 03 5F D6"},
    {"CRTDMG", 0x218629C, "E0 EF BF D2 C0 03 5F D6"},
    {"IMU", 0x2186348, "E0 EF BF D2 C0 03 5F D6"},
    {"REGEN", 0x21863F4, "E0 EF BF D2 C0 03 5F D6"},
    {"LIFESTEAL", 0x21864A0, "E0 EF BF D2 C0 03 5F D6"},
    {"STUNRATE", 0x218654C, "E0 EF BF D2 C0 03 5F D6"},
    {"DODGE", 0x21865F8, "E0 EF BF D2 C0 03 5F D6"},
    {"COMBO_HIT", 0x21866A4, "E0 EF BF D2 C0 03 5F D6"},
    {"ACCURACY", 0x2186750, "E0 EF BF D2 C0 03 5F D6"},
    {"CurrentSpeed", 0x23A7C70, "E0 EF BF D2 C0 03 5F D6"},
    {"CurrentStrength", 0x23A1EDC, "E0 EF BF D2 C0 03 5F D6"},
    {"MaxLevel", 0x214C01C, "E0 7B 40 B2 C0 03 5F D6"},
    {"SkillLevel", 0x2217938, "E0 7B 40 B2 C0 03 5F D6"}
};

// Helper functions
bool InitializeKittyMemory(const std::string& processName) {
    pid_t processID = KittyMemoryEx::getProcessID(processName);
    if (!processID) {
        KITTY_LOGI("Couldn't find process id of %s.", processName.c_str());
        return false;
    }

    KITTY_LOGI("Process Name: %s", processName.c_str());
    KITTY_LOGI("Process ID: %d", processID);

    if (!kittyMemMgr.initialize(processID, EK_MEM_OP_SYSCALL, true)) {
        KITTY_LOGI("Error occurred )':");
        return false;
    }

    return true;
}

bool FindIL2CPPBase() {
    do {
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
        
        g_il2cppElf = kittyMemMgr.getMemElf("libil2cpp.so");

        for (auto& it : KittyMemoryEx::getAllMaps(kittyMemMgr.processID())) {
            if (KittyUtils::String::Contains(it.pathname, kittyMemMgr.processName()) && 
                KittyUtils::String::EndsWith(it.pathname, ".apk")) {
                g_il2cppElf = kittyMemMgr.getMemElfInZip(it.pathname, "libil2cpp.so");
                if (g_il2cppElf.isValid()) break;
            }
        }

    } while (!g_il2cppElf.isValid() && g_AppRunning);
    
    if (g_il2cppElf.isValid()) {
        il2cppBase = g_il2cppElf.base();
        KITTY_LOGI("libil2cpp.so base: %p", (void *)il2cppBase);
        return true;
    }
    return false;
}

void InitializePatches() {
    for (auto& patchInfo : patches) {
        patchInfo.patch = kittyMemMgr.memPatch.createWithHex(
            il2cppBase + patchInfo.offset, 
            patchInfo.hexBytes
        );
    }
}

void TogglePatch(PatchInfo& patchInfo) {
    if (patchInfo.enabled) {
        if (patchInfo.patch.Modify()) {
            KITTY_LOGI("%s has been enabled successfully", patchInfo.name.c_str());
        }
    } else {
        if (patchInfo.patch.Restore()) {
            KITTY_LOGI("%s has been disabled successfully", patchInfo.name.c_str());
        }
    }
}

uintptr_t ParseHexString(const std::string& hexStr) {
    uintptr_t result = 0;
    std::string cleanHex = hexStr;
    
    if (cleanHex.length() >= 2 && (cleanHex.substr(0, 2) == "0x" || cleanHex.substr(0, 2) == "0X")) {
        cleanHex = cleanHex.substr(2);
    }
    
    for (char c : cleanHex) {
        result *= 16;
        if (c >= '0' && c <= '9') {
            result += c - '0';
        } else if (c >= 'A' && c <= 'F') {
            result += c - 'A' + 10;
        } else if (c >= 'a' && c <= 'f') {
            result += c - 'a' + 10;
        } else {
            KITTY_LOGI("Error parsing hex string: invalid character '%c' in '%s'", c, hexStr.c_str());
            return 0;
        }
    }
    
    return result;
}

void ShowHelp() {
    KITTY_LOGI("=== Available Commands ===");
    KITTY_LOGI("help          - Show this help");
    KITTY_LOGI("list          - List all patches");
    KITTY_LOGI("enable <id>   - Enable patch by ID (1-17)");
    KITTY_LOGI("disable <id>  - Disable patch by ID (1-17)");
    KITTY_LOGI("enableall     - Enable all patches");
    KITTY_LOGI("disableall    - Disable all patches");
    KITTY_LOGI("custom        - Add custom patch (il2cpp + offset + extra)");
    KITTY_LOGI("status        - Show current status");
    KITTY_LOGI("exit          - Exit application");
}

void ListPatches() {
    KITTY_LOGI("=== Patch List ===");
    for (size_t i = 0; i < patches.size(); i++) {
        KITTY_LOGI("%zu. %s (0x%lX) - %s", 
                  i + 1, 
                  patches[i].name.c_str(), 
                  patches[i].offset,
                  patches[i].enabled ? "ENABLED" : "DISABLED");
    }
}

void ProcessCommand(const std::string& command) {
    std::istringstream iss(command);
    std::string cmd;
    iss >> cmd;
    
    if (cmd == "help") {
        ShowHelp();
    } else if (cmd == "list") {
        ListPatches();
    } else if (cmd == "enableall") {
        for (auto& patch : patches) {
            if (!patch.enabled) {
                patch.enabled = true;
                TogglePatch(patch);
            }
        }
        KITTY_LOGI("All patches enabled!");
    } else if (cmd == "disableall") {
        for (auto& patch : patches) {
            if (patch.enabled) {
                patch.enabled = false;
                TogglePatch(patch);
            }
        }
        KITTY_LOGI("All patches disabled!");
    } else if (cmd == "enable") {
        int id;
        if (iss >> id && id >= 1 && id <= (int)patches.size()) {
            auto& patch = patches[id - 1];
            if (!patch.enabled) {
                patch.enabled = true;
                TogglePatch(patch);
            } else {
                KITTY_LOGI("Patch %s is already enabled", patch.name.c_str());
            }
        } else {
            KITTY_LOGI("Invalid patch ID. Use 'list' to see available patches.");
        }
    } else if (cmd == "disable") {
        int id;
        if (iss >> id && id >= 1 && id <= (int)patches.size()) {
            auto& patch = patches[id - 1];
            if (patch.enabled) {
                patch.enabled = false;
                TogglePatch(patch);
            } else {
                KITTY_LOGI("Patch %s is already disabled", patch.name.c_str());
            }
        } else {
            KITTY_LOGI("Invalid patch ID. Use 'list' to see available patches.");
        }
    } else if (cmd == "custom") {
        KITTY_LOGI("Custom patch format: il2cpp + base_offset + extra_offset");
        KITTY_LOGI("Example: il2cpp + 0x1223443 + 0x10");
        KITTY_LOGI("Enter base offset (e.g., 0x1223443): ");
        // For console app, we'll use predefined example
        uintptr_t baseOffset = 0x1223443;
        uintptr_t extraOffset = 0x10;
        std::string hexBytes = "E0 EF BF D2 C0 03 5F D6";
        
        uintptr_t finalOffset = baseOffset + extraOffset;
        PatchInfo customPatch("Custom", finalOffset, hexBytes, true);
        customPatch.patch = kittyMemMgr.memPatch.createWithHex(
            il2cppBase + finalOffset, 
            hexBytes
        );
        
        patches.push_back(customPatch);
        KITTY_LOGI("Added custom patch at offset 0x%lX", finalOffset);
    } else if (cmd == "status") {
        KITTY_LOGI("=== Status ===");
        KITTY_LOGI("IL2CPP Base: 0x%lX", il2cppBase);
        KITTY_LOGI("Process: %s (PID: %d)", 
                  kittyMemMgr.processName().c_str(), 
                  kittyMemMgr.processID());
        int enabled = 0;
        for (const auto& patch : patches) {
            if (patch.enabled) enabled++;
        }
        KITTY_LOGI("Patches: %d/%zu enabled", enabled, patches.size());
    } else if (cmd == "exit") {
        g_AppRunning = false;
    } else {
        KITTY_LOGI("Unknown command: %s. Type 'help' for available commands.", cmd.c_str());
    }
}

int main(int argc, char *args[]) {
    if (argc < 2) {
        KITTY_LOGE("Missing arg (process name).");
        return 1;
    }

    std::string processName = args[1];
    
    if (!InitializeKittyMemory(processName)) {
        return 1;
    }

    KITTY_LOGI("================ GET ELF BASE ===============");
    
    std::thread il2cppThread([]() {
        if (FindIL2CPPBase()) {
            InitializePatches();
            KITTY_LOGI("Patches initialized successfully");
        }
    });
    
    // Wait for IL2CPP base
    while (il2cppBase == 0 && g_AppRunning) {
        std::this_thread::sleep_for(std::chrono::milliseconds(500));
        KITTY_LOGI("Waiting for IL2CPP base...");
    }
    
    if (il2cppBase != 0) {
        KITTY_LOGI("=== KittyMemory IL2CPP Patcher Ready ===");
        ShowHelp();
        
        // Auto-enable all patches
        KITTY_LOGI("Auto-enabling all patches...");
        for (auto& patch : patches) {
            if (!patch.enabled) {
                patch.enabled = true;
                TogglePatch(patch);
            }
        }
        
        KITTY_LOGI("=== All patches are now active! ===");
        KITTY_LOGI("Patches will remain active. Press Ctrl+C to exit.");
        
        // Keep running
        while (g_AppRunning) {
            std::this_thread::sleep_for(std::chrono::seconds(1));
        }
    }
    
    if (il2cppThread.joinable()) {
        il2cppThread.join();
    }
    
    KITTY_LOGI("Application terminated");
    return 0;
}
