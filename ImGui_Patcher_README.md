# KittyMemory ImGui IL2CPP Patcher

This is an enhanced version of the KittyMemory example that uses ImGui for a user-friendly interface with on/off toggle functionality and custom patch support.

## Features

### ✅ ImGui Interface
- Modern, user-friendly graphical interface
- Real-time patch status display
- Easy-to-use toggle switches for each patch

### ✅ On/Off Functionality
- Individual patch enable/disable toggles
- "Enable All" and "Disable All" buttons
- Real-time patch status feedback

### ✅ Custom Patch Support
- Add custom patches with format: `il2cpp + offset + extra_offset`
- Example: `il2cpp + 0x1223443 + 0x10`
- Input fields for:
  - Patch name
  - Base offset (e.g., `0x1223443`)
  - Extra offset (e.g., `0x10`)
  - Hex bytes (e.g., `E0 EF BF D2 C0 03 5F D6`)

### ✅ Predefined Patches
The following patches are pre-configured:
- HP, ATK, DEF, SPEED
- CRT, CRTDMG, IMU, REGEN
- LIFESTEAL, STUNRATE, DODGE
- COMBO_HIT, ACCURACY
- CurrentSpeed, CurrentStrength
- MaxLevel, SkillLevel

## Usage

### Console Version (Main.cpp)
```bash
./EnemySaga com.target.process
```

The console version provides a simplified ImGui interface that logs patch operations.

### Android App Integration (AndroidImGuiExample.cpp)
For a full Android app with proper OpenGL rendering:

1. **Include the files in your Android project**
2. **Add to your CMakeLists.txt or Android.mk:**
   ```cmake
   # Add ImGui sources
   file(GLOB IMGUI_SRC ${IMGUI_PATH}/*.cpp ${IMGUI_PATH}/backends/*.cpp)
   
   # Add to your target
   target_sources(YourTarget PRIVATE 
       Main.cpp 
       AndroidImGuiExample.cpp 
       ${KITTYMEMORYEX_SRC} 
       ${IMGUI_SRC}
   )
   ```

3. **Create an Android Activity with OpenGL context** (see example in AndroidImGuiExample.cpp)

## Custom Patch Format

To create a custom patch with the format `il2cpp + 0x1223443 + 0x10`:

1. **Patch Name**: Enter a descriptive name (e.g., "Custom Damage")
2. **Base Offset**: Enter the main offset (e.g., `0x1223443`)
3. **Extra Offset**: Enter additional offset (e.g., `0x10`)
4. **Hex Bytes**: Enter the patch bytes (e.g., `E0 EF BF D2 C0 03 5F D6`)

The final address will be calculated as: `il2cpp_base + base_offset + extra_offset`

## Interface Controls

### Main Window Sections:
1. **Status Display**: Shows IL2CPP base address and process info
2. **Predefined Patches**: Table with all pre-configured patches
3. **Custom Patch**: Form to add new patches
4. **Control Buttons**: Enable/Disable all patches, Exit

### Patch Table Columns:
- **Patch Name**: Descriptive name of the patch
- **Offset**: Memory offset from IL2CPP base
- **Enable/Disable**: Checkbox to toggle patch on/off

## Building

### Requirements:
- Android NDK
- KittyMemoryEx library
- ImGui library
- C++17 or higher

### Build Commands:
```bash
# Using NDK Build
ndk-build

# Using CMake
cmake --build cmake_builds/arm64-v8a

# Using provided scripts
./ndk_build.bat    # Windows
./build.bat        # Windows
```

## Deployment

1. **Build the executable/library**
2. **Push to Android device:**
   ```bash
   adb push libs/arm64-v8a/EnemySaga /data/local/tmp
   adb shell "su -c 'chmod +x /data/local/tmp/EnemySaga'"
   ```

3. **Run with target process:**
   ```bash
   adb shell "su -c '/data/local/tmp/EnemySaga com.target.process'"
   ```

## Notes

- **Root access required** for memory patching
- **Target process must be running** before starting the patcher
- **IL2CPP library must be loaded** in the target process
- For Android apps, ensure proper **OpenGL context** is available for ImGui rendering

## Troubleshooting

### Common Issues:
1. **"Process not found"**: Ensure target process is running
2. **"IL2CPP not found"**: Wait for the target app to fully load
3. **"Permission denied"**: Ensure root access and proper permissions
4. **ImGui not rendering**: Check OpenGL context setup in Android app

### Debug Output:
Monitor logcat for detailed information:
```bash
adb logcat -s "KittyMemoryEx"
```
